	<script>
		$(document).ready(function() {
			$('.download').click(function() {
				$('.download').hide();
				window.print();
			});
			$(window).on("afterprint", function(event) {
				$('.download').show();
				window.close();
			});
		});
	</script>
	<style type="text/css" media="print">
		@media print {
			@page {
				size: 200mm 150mm;
				margin: 0;
			}

			body {
				margin: 0;
				padding: 0;
				width: 200mm;
				height: 150mm;
				overflow: hidden;
				transform: scale(0.95);
				transform-origin: top left;
			}

			.container1 {
				margin: 0 auto;
				padding: 10mm;
				width: 180mm;
				height: auto;
				box-sizing: border-box;
			}

			* {
				page-break-before: avoid;
				page-break-after: avoid;
				page-break-inside: avoid;
			}

			.no-print {
				display: none !important;
			}
		}
	</style>