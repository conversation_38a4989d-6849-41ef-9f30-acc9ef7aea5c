  <style>
      body {
          background-color: #048E8E;
      }

      .container1 {
          background-color: #fff;
          border-radius: 10px;
          padding: 20px;
          margin-top: 10%;
          margin-bottom: 10px;
          margin-left: 10%;
          margin-right: 10%;
      }

      .container-doctor {

          background-color: #fff;
          border-radius: 10px;
          padding: 20px;
          margin-bottom: 10%;
          margin-left: 10%;
          margin-right: 10%;
      }

      .container-input .form-control {
          text-align: center !important;
          margin: 0 auto;
          margin-bottom: 10px;
      }

      .btn-input {
          background-color: #048E8E !important;
          color: white;
          width: 250px;
          border-radius: 10px;
      }

      .btn-tgllahir {
          width: 200px;
      }

      .text-validasi-error {
          color: red;
      }

      .col-validasi {
          margin: 20px 0px 20px 0px;
      }

      .btn-pasienbaru {
          background-color: white !important;
          border-color: #048E8E !important;
          color: #048E8E;
      }

      .form-control {
          width: 250px !important;
      }

      .table-pasien {
          color: #048E8E;
      }

      .container-pasien {
          margin-bottom: 30px;
          font-size: medium;
      }

      .table-pasien td {
          vertical-align: top;
          text-align: left;
      }

      .img-doctor {
          width: 100px;
      }

      .btn-lihatjadwal {
          background-color: #048E8E !important;
          color: white !important;
      }

      .arrow {
          position: absolute;
          top: 50%;
          transform: translateY(-50%);
          font-size: 30px;
          width: 20px;
          height: 20px;
          line-height: 20px;
          text-align: center;
          color: #fff;
          border-radius: 50%;
          cursor: pointer;
      }

      .arrow-left {
          left: -15px;
      }

      .arrow-right {
          right: -15px;
      }

      .date-container {
          text-align: center;
      }

      .date {
          padding: 0;
          list-style: none;
          display: flex;
          justify-content: center;
      }

      .date li {
          display: inline-block;
          margin: 0 3px;
          padding: 2px;
          border: 1pt solid white;
          border-radius: 5px;
      }

      .date p {
          padding: 0 0 0 0;
          margin: 0 0 0 0;
          color: white;
      }

      .date a {
          padding: 0 0 0 0;
          margin: 0 0 0 0;
          cursor: pointer;
      }

      .date li a {
          display: inline-block;
          text-decoration: none;
          color: black;
          border: 1px solid transparent;
      }

      .date li a:hover,
      .date li a:active {
          background-color: #6B6B6B;
          color: white;
          border-color: dark;
          cursor: pointer;
          border-radius: 5px;
      }

      .arrow-date {
          position: absolute;
          top: 70%;
          transform: translateY(-50%);
          font-size: 30px;
          width: 20px;
          height: 20px;
          line-height: 20px;
          text-align: center;
          cursor: pointer;
      }

      .arrow-date a {
          color: white !important;
      }

      .arrow-date-left {
          left: 10px;
      }

      .arrow-date-right {
          right: 10px;
      }

      .doctor-container {
          background-color: #048E8E;
          color: white !important;
          border-radius: 2px;
      }

      .container-list-jadwal h2.text-center {
          color: #048E8E;
          padding: 0;
          margin: 0;
          padding-bottom: 10px;

      }

      .container-list-jadwal h4.text-center {
          padding: 0;
          margin: 0;
      }

      .container-list-jadwal h5.text-center {
          padding: 0;
          margin: 0;
          padding-bottom: 10px;
      }

      .container-list-jadwal h6.text-center {
          color: #74C3A2;
          padding: 0;
          margin: 0;
      }

      .Keluhan {
          margin-top: 10px;
          height: 50px;
          color: #048E8E;
          width: 100% !important;
          border: 1pt solid #048E8E;
      }
  </style>