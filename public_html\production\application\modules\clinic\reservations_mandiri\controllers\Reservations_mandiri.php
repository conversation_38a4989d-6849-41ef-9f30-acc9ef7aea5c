<?php if (!defined('BASEPATH')) exit('No direct script access allowed');

class Reservations_mandiri extends Admin_Controller
{
	protected $_translation = 'reservations_mandiri';
	//protected $_model = 'reservation_m'; 

	public function __construct()
	{
		parent::__construct();

		$this->page = "common_reservations_mandiri";
		$this->template->title(lang("reservations_mandiri:page") . ' - ' . $this->config->item('company_name'));

		$this->load->helper('reservation_mandiri');
		$this->load->model('reservation_m');
		$this->load->model('common/patient_type_m');
		$this->load->model('common/section_m');
		$this->load->model('common/supplier_m');
		$this->load->model("regional_model");
		$this->load->model("vw_regional_model");
	}

	public function index()
	{
		$data = array(
			"page" => $this->page,
			"form" => TRUE,
			"datatables" => TRUE,
		);

		$this->template
			->set("heading", "Reservasi")
			->set_breadcrumb(lang("reservations_mandiri:breadcrumb"))
			->build('reservations_mandiri/index', (isset($data) ? $data : NULL));
	}

	public function view($NoReservasi = NULL)
	{
		$Reservasi = $this->db->where('NoReservasi', $NoReservasi)->get('SIMtrReservasi')->row();
		$Section = $this->db->where('SectionID', $Reservasi->UntukSectionID)->get('SIMmSection')->row();
		$Dokter = $this->db->where('Kode_Supplier', $Reservasi->UntukDokterID)->get('mSupplier')->row();
		$data = array(
			"reservasi" => (object) $Reservasi,
			"Section" => (object) $Section,
			"Dokter" => (object) $Dokter,
			"page" => $this->page,
			"form" => TRUE,
			"datatables" => TRUE,
		);

		$this->template
			->set("heading", "Reservasi View")
			->set_breadcrumb(lang("reservations_mandiri:breadcrumb"))
			->build('reservations_mandiri/index_view', (isset($data) ? $data : NULL));
	}

	public function lookup_doctor()
	{
		$data = $this->db->select('a.Kode_Supplier, a.Nama_Supplier, b.Nama_Kategori, c.SpesialisName, d.SubSpesialisName')
			->from('mSupplier a')
			->join('mKategori b', 'a.KodeKategoriVendor=b.Kode_Kategori', 'LEFT')
			->join('SIMmSpesialisasi c', 'a.SpesialisID=c.SpesialisID', 'LEFT')
			->join('SIMmSubSpesialis d', 'a.SubSpesialisID=d.SubSpesialisID', 'LEFT')
			->where('ReservasiMandiri', 1)
			->where_in('KodeKategoriVendor', ['V-002'])
			->get()->result();

		$output = array(
			'data' => array()
		);

		foreach ($data as $key => $value) {
			if ($value->Kode_Supplier != 'XX') {
				$output['data'][] = $value;
			}
		}
		$this->template
			->build_json($output);
	}

	public function Validasi_pasien()
	{
		$where = [];

		$NRM = $this->input->post('NRM');
		$TanggalLahir = $this->input->post('TanggalLahir');

		$where['NRM'] = $NRM;
		$where['TglLahir'] = $TanggalLahir;

		$data = $this->db->select('NRM, NamaPasien, TglLahir, Alamat')
			->from('mPasien')
			->where($where)
			->get()->row();

		if (!empty($data)) {
			$message = [
				"status" => 'success',
				"message" => 'Data Pasien Tersedia',
				"code" => 200,
				"data" => $data,
			];
		} else {
			$message = [
				"status" => 'erorr',
				"message" => 'Data Pasien Tidak Tersedia',
				"code" => 500,
			];
		}

		$this->template
			->build_json($message);
	}

	public function lookup_jadwal()
	{
		$Kode_Supplier = $this->input->post('Kode_Supplier');
		$Tanggal = $this->input->post('Tanggal');

		$DataJadwal = $this->db->select('*')
			->from('SIMtrDokterJagaDetail a')
			->join('SIMmWaktuPraktek b', 'a.WaktuID=b.WaktuID')
			->join('SIMmSection c', 'a.SectionID=c.SectionID')
			->where([
				'Tanggal' => $Tanggal,
				'DokterID' => $Kode_Supplier
			])
			->get()->result();

		if (!empty($DataJadwal)) {
			$message = [
				"status" => 'success',
				"message" => 'Data Jadwal Tersedia',
				"code" => 200,
				"data" => $DataJadwal,
			];
		} else {
			$message = [
				"status" => 'erorr',
				"message" => 'Data Jadwal Tidak Tersedia',
				"code" => 500,
			];
		}
		$this->template
			->build_json($message);
	}


	public function create()
	{
		if ($this->input->post()) {
			$item = (object)$this->input->post();
			$Pasien = $this->db->where('NRM', $item->NRM)->get('mPasien')->row();
			$CheckReservasi = $this->db->where([
				'UntukSectionID' => @$item->UntukSectionID,
				'UntukDokterID' => @$item->UntukDokterID,
				'UntukTanggal' => date('Y-m-d', strtotime(@$item->UntukTanggal)),
				'WaktuID' => @$item->WaktuReservasi,
				'NRM' => @$item->NRM,
				'JenisKerjasamaID' => 3,
			])->get("SIMtrReservasi")->row();
			if (!empty($CheckReservasi)) {
				$this->template
					->build_json([
						"status" => 'error',
						"message" => 'Data Reservasi Sudah Tersedia',
						"NoReservasi" => $CheckReservasi->NoReservasi,
						"code" => 500,
					]);
			} else {
				$hari_eng = date('l', strtotime($item->UntukTanggal)); // Mendapatkan hari dalam bahasa Inggris
				$hari = "";
				switch ($hari_eng) {
					case 'Monday':
						$hari = 'SENIN';
						break;
					case 'Tuesday':
						$hari = 'SELASA';
						break;
					case 'Wednesday':
						$hari = 'RABU';
						break;
					case 'Thursday':
						$hari = 'KAMIS';
						break;
					case 'Friday':
						$hari = 'JUMAT';
						break;
					case 'Saturday':
						$hari = 'SABTU';
						break;
					case 'Sunday':
						$hari = 'MINGGU';
						break;
					default:
						$hari = 'HARI TIDAK VALID';
						break;
				}
				$data = [
					'NoReservasi' => reservation_mandiri_helper::gen_reservation_number(),
					'Tanggal' => date('Y-m-d'),
					'Jam' => date('Y-m-d H:i:s'),
					'PasienBaru' => 0,
					'NRM' => @$item->NRM,
					'Nama' => @$Pasien->NamaPasien,
					'Alamat' => @$Pasien->Alamat,
					'Phone' => @$Pasien->Phone,
					'UntukSectionID' => @$item->UntukSectionID,
					'UntukDokterID' => @$item->UntukDokterID,
					'UntukHari' => @$hari,
					'UntukTanggal' => date('Y-m-d', strtotime(@$item->UntukTanggal)),
					'UntukJam' => date('Y-m-d', strtotime(@$item->UntukTanggal)) . " " . date("H:i:s"),
					'NoUrut' => reservation_mandiri_helper::get_reservation_queue(@$item->UntukSectionID, @$item->UntukDokterID, @$item->UntukTanggal, @$item->WaktuReservasi, 3),
					'User_ID' => 0,
					'Registrasi' => 0,
					'JmlAntrian' => NULL,
					'WaktuID' => @$item->WaktuReservasi,
					'JenisKerjasamaID' => 3,
					'TanggalLahir' => @$Pasien->TglLahir,
					'Memo' => @$item->Keluhan,
					'Email' => @$Pasien->Email,
					'Batal' => 0,
					'Paid' => 0,
					'TipeReservasi' => 'RESERVASI POLI',
					'NIK' => @$Pasien->NoIdentitas,
				];

				if ($this->db->insert("SIMtrReservasi", $data)) {
					$message = [
						"status" => 'success',
						"message" => 'Data Reservasi Barhasil Disimpan',
						"code" => 200,
						"NoReservasi" => $data['NoReservasi'],
					];
				} else {
					$message = [
						"status" => 'erorr',
						"message" => 'Data Reservasi Tidak Berhasil Disimpan',
						"code" => 500,
					];
				}

				$this->template
					->build_json($message);
			}
		}
	}

	public function get_reservation_queue($UntukSectionID, $UntukDokterID, $UntukTanggal, $WaktuID, $JenisKerjasamaID)
	{
		$response = array(
			"status" => "success",
			"message" => "",
			"code" => 200,
			"NoUrut" => reservation_mandiri_helper::get_reservation_queue($UntukSectionID, $UntukDokterID, $UntukTanggal, $WaktuID, $JenisKerjasamaID)
		);
		response_json($response);
	}
}
