<script>
	$(document).ready(function() {
		var tempDoctor;
		var currentIndexDoctor = 0;
		var currentDate = new Date(); // Mendapatkan tanggal saat ini
		var dateIndex = currentDate.getDate() - 2; // Mengatur index tanggal untuk memulai dari tanggal saat ini
		var currentMonth = currentDate.getMonth() + 1; // January is 0
		var currentYear = currentDate.getFullYear();
		var dates = [];
		var dayNames = ['MINGGU', 'SENIN', 'SELASA', 'RABU', 'KAMIS', 'JUMAT', 'SABTU']; // Short day names in Indonesian

		// Function to update dates array with current month's dates
		function updateDates() {
			var daysInMonth = new Date(currentYear, currentMonth, 0).getDate();
			dates = [];
			for (var i = 1; i <= daysInMonth; i++) {
				dates.push(i);
			}
		}

		// Initialize dates array with current month's dates
		updateDates();

		// Display initial date
		displayDate();

		// Function to display date
		function displayDate() {
			var monthNames = ['<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', 'April', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>gus<PERSON>', 'September', 'Oktober', 'November', 'Desember'];
			$('#month').text(monthNames[currentMonth - 1] + ' ' + currentYear);
			var dateHtml = '';

			for (var i = dateIndex; i < Math.min(dateIndex + 4, dates.length); i++) {
				var date = dates[i];
				var day = new Date(currentYear, currentMonth - 1, date).getDay();
				var dayName = dayNames[day];
				var formattedDate = date.toString().padStart(2, '0');
				var formattedMonth = currentMonth.toString().padStart(2, '0');

				dateHtml += '<li>';
				dateHtml += "<a type=\"button\" id=" + currentYear + '-' + formattedMonth + '-' + formattedDate + " class=\"btn-date\">";
				dateHtml += '<p><b>' + formattedDate + '</b></p>';
				dateHtml += '<p><b>' + dayName + '</b></p>';
				dateHtml += '</a>';
				dateHtml += '</li>';
			}
			$('.date').html(dateHtml);
		}

		// Next button click event
		$('#nextBtn').click(function() {
			dateIndex += 4;
			if (dateIndex >= dates.length) {
				dateIndex = 0;
				currentMonth++;
				if (currentMonth > 12) {
					currentMonth = 1;
					currentYear++;
				}
				updateDates();
			}
			displayDate();
		});

		// Previous button click event
		$('#prevBtn').click(function() {
			dateIndex -= 4;
			if (dateIndex < 0) {
				currentMonth--;
				if (currentMonth < 1) {
					currentMonth = 12;
					currentYear--;
				}
				updateDates();
				dateIndex = Math.max(0, dates.length - 7);
			}
			displayDate();
		});

		function fetchDataDoctor() {
			$.ajax({
				url: '<?= base_url('/reservations_mandiri/lookup_doctor') ?>',
				method: 'GET',
				success: function(response) {
					tempDoctor = response.data;
					displayCurrentDataDoctor();

				},
				error: function(xhr, status, error) {
					console.error(xhr.responseText);
				}
			});
		}

		fetchDataDoctor();

		$('#nextBtnDoctor').click(function() {
			if (currentIndexDoctor < tempDoctor.length - 1) {
				currentIndexDoctor++;
			} else {
				currentIndexDoctor = 0; // Reset ke indeks 0 jika sudah mencapai akhir data
			}
			displayCurrentDataDoctor();
		});

		// Previous button click event
		$('#prevBtnDoctor').click(function() {
			if (currentIndexDoctor > 0) {
				currentIndexDoctor--;
			} else {
				currentIndexDoctor = tempDoctor.length - 1; // Reset ke akhir data jika berada di indeks 0
			}
			displayCurrentDataDoctor();

		});


		function displayCurrentDataDoctor() {
			$('#DoktorImg').attr("src", "<?= base_url('public/resource/images/dokter/') ?>" + tempDoctor[currentIndexDoctor].Kode_Supplier + '.png');
			$('#Kode_Supplier').val(tempDoctor[currentIndexDoctor].Kode_Supplier);
			$('#Nama_Supplier').text(tempDoctor[currentIndexDoctor].Nama_Supplier);
			$('#SpesialisName').text('Spesialis ' + tempDoctor[currentIndexDoctor].SpesialisName);
			$('.container-list-jadwal').slideUp(400, function() {
				$('.container-list-jadwal').hide();
			});
		}

		function getJadwalDoctor(date) {

			$.ajax({
				url: '<?= base_url('/reservations_mandiri/lookup_jadwal') ?>',
				method: 'POST',
				data: {
					Kode_Supplier: $('#Kode_Supplier').val(),
					Tanggal: date
				},
				success: function(response) {
					var dateHtmlJadwalDoctor = '';

					if (response.status == 'success') {
						$.each(response.data, function(key, value) {
							var encodedData = encodeURIComponent(JSON.stringify(value));
							dateHtmlJadwalDoctor += '<div class="row ">';
							dateHtmlJadwalDoctor += '<div class="col-md-6 col-md-offset-3">';
							dateHtmlJadwalDoctor += '<div class="well well-sm ">';
							dateHtmlJadwalDoctor += '<h2 class="text-center">RINDANI FARMA</h2>';
							dateHtmlJadwalDoctor += '<h4 class="text-center">' + value.SectionName + '</h4>';
							dateHtmlJadwalDoctor += '<h5 class="text-center">' + value.NamaWaktu + '</h5>';
							dateHtmlJadwalDoctor += '<h6 class="text-center">Tersedia</h6>';
							dateHtmlJadwalDoctor += '<button type="button" class="btn btn-default btn-input reservasi" data-value="' + encodedData + '">RESERVASI</button>';
							dateHtmlJadwalDoctor += '</div>';
							dateHtmlJadwalDoctor += '</div>';
							dateHtmlJadwalDoctor += '</div>';
							$('.container-list-jadwal').html(dateHtmlJadwalDoctor);
						});
					}

					if (response.status == 'erorr') {
						dateHtmlJadwalDoctor += '<div class="row ">';
						dateHtmlJadwalDoctor += '<div class="col-md-6 col-md-offset-3">';
						dateHtmlJadwalDoctor += '<div class="well well-sm ">';
						dateHtmlJadwalDoctor += '<h5 class="text-center" style="color:red;">Data Jadwal Tidak Tersedia</h5>';
						dateHtmlJadwalDoctor += '</div>';
						dateHtmlJadwalDoctor += '</div>';
						dateHtmlJadwalDoctor += '</div>';

						$('.container-list-jadwal').html(dateHtmlJadwalDoctor);

					}
					$('.container-list-jadwal').hide().slideDown(500);
					$('html, body').animate({
						scrollTop: $(".container-list-jadwal").offset().top + $(".container-list-jadwal").height()
					}, 500);

				},
				error: function(xhr, status, error) {
					console.error(xhr.responseText);
				}
			});
		}

		$(document).on('click', '.btn-date', function() {
			console.log(123);
			var date = $(this).attr('id')
			$('.container-list-jadwal').hide()
			getJadwalDoctor(date)
		});

		$(document).on('click', '.reservasi', function() {
			var dataValue = $(this).data("value");
			var dataObject = JSON.parse(decodeURIComponent(dataValue));
			console.log(dataObject);

			$("#NRMReservasi").text($("#NRMPasien").text());
			$("#NamaReservasi").text($("#NamaPasien").text());
			$("#TglLahirReservasi").text($("#TglLahirPasien").text());
			$("#AlamatReservasi").text($("#AlamatPasien").text());
			$("#DokterReservasi").text($("#Nama_Supplier").text());
			$("#PoliReservasi").text(dataObject.SectionName);
			$("#WaktuReservasi").text(dataObject.Keterangan);

			$("#UntukSectionIDReservasi").val(dataObject.SectionID);
			$("#UntukDokterIDReservasi").val(dataObject.DokterID);
			$("#UntukTanggalReservasi").val(dataObject.Tanggal);
			$("#WaktuReservasi").val(dataObject.WaktuID);
			$('#reservasi').modal('show');
		});

		$(document).on('click', '.save-reservasi', function() {
			$.ajax({
				url: '<?= base_url('/reservations_mandiri/create') ?>',
				method: 'POST',
				data: {
					NRM: $("#NRMReservasi").text(),
					UntukDokterID: $("#UntukDokterIDReservasi").val(),
					UntukSectionID: $("#UntukSectionIDReservasi").val(),
					UntukTanggal: $("#UntukTanggalReservasi").val(),
					WaktuReservasi: $("#WaktuReservasi").val(),
					Keluhan: $("#Keluhan").val(),
				},
				success: function(response) {
					if (response.status == 'success') {
						document.location.href = "<?php echo base_url("reservations_mandiri/view/"); ?>" + response.NoReservasi;
					}

					if (response.NoReservasi) {
						document.location.href = "<?php echo base_url("reservations_mandiri/view/"); ?>" + response.NoReservasi;
					}

					if (response.status == 'error') {
						alert(response.message);
						document.location.href = "<?php echo base_url("reservations_mandiri/view/"); ?>" + response.NoReservasi;
					}
				},
				error: function(xhr, status, error) {
					console.error(xhr.responseText);
				}
			});
		});

	});
</script>