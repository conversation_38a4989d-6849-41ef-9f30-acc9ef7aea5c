	<script>
		function formatInput(inputValue) {
			// Hapus semua karakter selain angka
			var cleaned = inputValue.replace(/\D/g, '');

			// Format ke bentuk yang diinginkan (00.00.00)
			var formatted = '';
			for (var i = 0; i < cleaned.length; i++) {
				if (i === 2 || i === 4) {
					formatted += '.';
				}
				formatted += cleaned[i];
			}
			return formatted;
		}

		$('#Input-NRM').on('input', function() {
			var inputValue = $(this).val();
			var formattedValue = formatInput(inputValue);
			$(this).val(formattedValue);
		});

		function validasi_pasien() {
			var NRM = $('#Input-NRM').val()
			var TanggalLahir = $('#Input-TanggalLahir').val()
			var NRMDigit = NRM.replace(/\D/g, '');
			console.log(NRMDigit);
			console.log(TanggalLahir);
			console.log(NRMDigit === '' || TanggalLahir === '');

			if (NRMDigit === '' || TanggalLahir === '') {
				$('.text-validasi-error').text('Silahkan Isi NRM dan Tanggal lahir');
				return false; // Kembalikan false jika salah satu atau kedua input kosong
			}

			$.ajax({
				url: '<?= base_url('/reservations_mandiri/validasi_pasien') ?>',
				method: 'POST',
				data: {
					NRM: NRM,
					TanggalLahir: TanggalLahir
				},
				success: function(response) {
					if (response.status == 'success') {
						$('#NRMPasien').text(response.data.NRM);
						$('#NamaPasien').text(response.data.NamaPasien);
						$('#TglLahirPasien').text(response.data.TglLahir.substring(0, 10));
						$('#AlamatPasien').text(response.data.Alamat);

						$('.container-input').slideUp(300, function() {
							$(this).hide()
						})
						$('.container-pasien').slideDown(300, function() {
							$(this).show()
						})
						$('.container-doctor').slideDown(300, function() {
							$(this).show()
							$('html, body').animate({
								scrollTop: $(".container-doctor").offset().top + $(".container-doctor").height()
							}, 500);
						})
					} else {
						$('.text-validasi-error').text(response.message);
					}
				},
				error: function(xhr, status, error) {
					console.error(xhr.responseText);
				}
			});
		}

		$('.btn').on('click', function() {
			var id = $(this).attr('id')
			if (id == 'NRM') {
				$('#NRM').hide()
				$('#Input-NRM').show()
				$('#TanggalLahir').hide()
				$('#Input-TanggalLahir').show()
			} else if (id == 'TanggalLahir') {
				$('#NRM').hide()
				$('#Input-NRM').show()
				$('#TanggalLahir').hide()
				$('#Input-TanggalLahir').show()
			} else if (id == 'Validasi') {
				validasi_pasien()
			} else if (id == 'LihatJadwal') {
				$('.container-input').slideUp(300, function() {
					$(this).hide()
				})
				$('.container-doctor').slideDown(300, function() {
					$(this).show()
				})
				$('.container-jadwal').slideDown(300, function() {
					$(this).show()
					$('html, body').animate({
						scrollTop: $(".container-jadwal").offset().top + $(".container-jadwal").height()
					}, 500);
				})
			}
		})

		$(document).on("ready", function(e) {
			$('#NRM').show()
			$('#Input-NRM').hide()
			$('#TanggalLahir').show()
			$('#Input-TanggalLahir').hide()

			$('.container-input').hide().slideDown(500);
			$('.container-pasien').hide().slideUp(500);
			$('.container-doctor').hide().slideUp(500);
			$('.container-jadwal').hide().slideUp(500);
			$('.container-list-jadwal').hide().slideUp(500);

		});
	</script>